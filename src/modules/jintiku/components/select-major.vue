<template>
  <view
    class="select-lesson mask"
    :class="{ show }"
    @click="close"
    :style="{ height: show ? '100%' : '0' }"
  >
    <!-- :style="{ height: isShow ? '100%' : '0' }" -->
    <view class="box" :class="{ show }" @click.stop="() => {}">
      <!-- <view class="title">选择对应校区下的开课城市</view> -->
      <view class="select-content">
        <view class="select-content-left">
          <view
            class="select-menu hide-text"
            :class="{ active: leftActiveId == item.id }"
            v-for="(item, index) in dataList"
            :key="index"
            @click.stop="getRightInfo(item)"
          >
            {{ item.data_name }}
          </view>
        </view>
        <view class="select-content-right">
          <view class="select-menus" v-for="(res, j) in rightInfos" :key="j">
            <view class="select-menus-name hide-text">
              {{ res.data_name }}
            </view>
            <view class="select-menus-boxs" v-if="res.subs && res.subs.length">
              <view
                class="select-menu-item hide-text"
                v-for="(item, index) in res.subs"
                :class="{ active: getActive(item) }"
                :key="index"
                @click.stop="change(item)"
              >
                {{ item.data_name }}
              </view>
            </view>
            <view class="select-menus-boxs" v-else>
              <view
                class="select-menu-item hide-text"
                :class="{ active: res.id == value }"
                @click.stop="change(res)"
              >
                {{ res.data_name }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { getMajor, checkMajor } from '../api/index'
import { transformDataId } from '../utils/index'
export default {
  props: {
    value: {
      type: String
    },
    show: {
      type: Boolean
    },
    major_name: {
      type: String
    }
  },
  created() {
    uni.$on('merch_clear', () => {
      this.rightInfos = []
    })
  },
  data() {
    return {
      leftActiveId: '426531466311180284',
      datas: [],
      dataList: [],
      rightInfos: [],
      isShow: false
    }
  },
  methods: {
	  getActive(item){
		  console.log(item.id, this.value)
		  return item.id == this.value;
	  },
    startFun() {
      let obj = {
        code: 'professional',
        is_auth: 2,
        is_usable: 1,
        professional_ids: ''
      }
      getMajor(obj).then(res => {
        this.dataList = transformDataId(res.data)
		console.log(this.dataList, '-----------0')
		if(this.rightInfos.length == 0){
			console.log('-----------1')
			let { major_pid_level = '' } = uni.getStorageSync('__xingyun_major__')
			if(!major_pid_level){
				console.log(major_pid_level, '-----------2--------major_pid_level')
				let ppItem = this.dataList.filter(ppIt => ppIt.pid == "497502623793548373")
				this.getRightInfo(ppItem[0]);
			}
		}
      })
    },
	
    getRightInfo(item) {
      this.leftActiveId = item.id
      this.rightInfos = item.subs
    },
    change(item) {
      let token = uni.getStorageSync('__xingyun_token__')
      if (token) {
        let { student_id = '' } = uni.getStorageSync('__xingyun_userinfo__')
        checkMajor({
          id: student_id,
          major_id: item.id
        }).then(res => {
          this.$xh.Toast('操作成功')
          this.$emit('input', item.id)
          this.$emit('change', item)
          this.$emit('update:show', false)
          this.$emit('update:major_name', item.data_name)
          let obj = {
            major_id: item.id,
            major_name: item.data_name,
			major_pid_level: item.level
          }
          uni.setStorageSync('__xingyun_major__', obj)
        })
        return
      }
      // 游客模式
      this.$xh.Toast('操作成功')
      this.$emit('input', item.id)
      this.$emit('change', item)
      this.$emit('update:show', false)
      this.$emit('update:major_name', item.data_name)
      let obj = {
        major_id: item.id,
        major_name: item.data_name,
		major_pid_level: item.level
      }
      uni.setStorageSync('__xingyun_major__', obj)
    },
    close() {
      this.$emit('update:show', false)
    }
  },
  watch: {
    show(value) {
      if (value) {
        this.isShow = true
        this.startFun()
      } else {
        setTimeout(() => {
          this.isShow = false
        }, 250)
      }
    }
  }
}
</script>
<style scoped lang="less">
.mask {
  position: fixed;
  left: 0;
  top: 176rpx;
  /* #ifdef H5 */
  top: 106rpx;
  /* #endif */
  width: 100%;
  height: 100%;
  transition: all 0.25s;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  z-index: 1000;
  .box {
    background-color: #fff;
    height: 0;
    transition: all 0.25s;
    overflow: hidden;
    .title {
      height: 72rpx;
      display: flex;
      align-items: center;
      padding-left: 24rpx;
      margin-bottom: 24rpx;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(3, 32, 61, 0.65);
    }
    .select-content {
      height: 960rpx;
      display: flex;
      .select-content-left {
        width: 184rpx;
        background: #f6f7f8;
        overflow-y: auto;
        padding-bottom: 30rpx;
        .select-menu {
          height: 96rpx;
          background: #f6f7f8;
          width: 100%;
          text-align: center;
          line-height: 96rpx;
          padding: 0 10rpx;
          font-size: 26rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #03203d;
        }
        .select-menu.active {
          background-color: #fff;
        }
      }
      .select-content-right {
        flex: 1;
        background-color: #fff;
        padding: 0 24rpx;
        padding-top: 30rpx;
        flex-wrap: wrap;
        justify-content: space-between;
        overflow-y: auto;
        height: 100%;
        .select-menus {
          .select-menus-name {
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(3, 32, 61, 0.65);
            margin-bottom: 24rpx;
          }
          .select-menus-boxs {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-between;
            .select-menu-item {
              border: 2rpx solid transparent;
              width: calc(50% - 11rpx);
              height: 68rpx;
              line-height: 68rpx;
              text-align: center;
              align-items: center;
              justify-content: center;
              border-radius: 34rpx;
              background: #f6f7f8;
              color: #03203d;
              font-size: 24rpx;
              transition: all 0.25s;
              margin-bottom: 32rpx;
              padding: 0 10rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .select-menu-item.active {
              background: #ebf1ff;
              border: 2rpx solid rgba(46, 104, 255, 0.5);
              color: #2e68ff;
            }
          }
        }
      }
    }
  }
  .show {
    height: 960rpx;
  }
}
.show {
  opacity: 1;
}
</style>
