<template>
  <view class="my-course">
    <ModuleStudyMyCourseTeachingTypeTab
      :value="teaching_type"
      @change="teachingChange"
    />
    <ModuleStudyCourse ref="studyPlanList" :list="list" />
    <ModuleStudyNotLearn v-if="!list.length && !loading" />
    <view v-if="loading" style="text-align: center; margin-top: 50px">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>
<script>
import { study } from '@/modules/jintiku/api/index.js'

export default {
  name: 'ModuleStudyMyCourse',
  data() {
    return {
      list: [],
      teaching_type: "3",
      loading: true,
      restrict: false,
      form: {
        page: 1,
        size: 10,
      },
      total: 0
    }
  },

  methods: {
    getData() {
      this.loading = true;
      study
        .myCourse({
          ...this.form,
          teaching_type: this.teaching_type,
        })
        .then(({ data }) => {
          this.restrict = false;
          if (data.list && data.list.length) {
            let copyList = data.list.map((item) => {
              let teacher = [];
              // 教师只展示前两个
              if (item.class.teacher && item.class.teacher && item.class.teacher.length) {
                if (item.class.teacher.length > 2) {
                  teacher = item.class.teacher.slice(0, 2);
                } else {
                  teacher = item.class.teacher;
                }
              }
              return {
                ...item,
                evaluation_type: item.evaluation_type.filter(
                  (btn) => btn.paper_version_id && btn.paper_version_id != "0"
                ),
                class: {
                  ...item.class,
                  teacher,
                },
              };
            });
            this.list = [...this.list, ...copyList];
            this.total = data.total;
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err.msg[0],
            icon: 'none'
          });
          if (err.code == 100002) {
            uni.showToast({
              title: err.msg[0],
              icon: 'none'
            });
            this.studyLoginLose();
          }
        })
        .finally(() => {
          this.restrict = false;
          this.loading = false;
        });
    },

    scrollGet(e) {
      // uni-app中的滚动监听处理
      const scrollTop = e.scrollTop;
      const systemInfo = uni.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;

      // 获取组件高度
      uni.createSelectorQuery().in(this).select('.my-course').boundingClientRect((data) => {
        if (data) {
          const scrollHeight = data.height;
          const fast = 100;

          if (scrollTop >= scrollHeight - windowHeight - fast) {
            if (this.restrict) {
              return;
            }
            if (this.form.page >= Math.ceil(this.total / 10)) {
              return;
            }
            this.restrict = true;
            this.form.page = this.form.page + 1;
            this.getData();
          }
        }
      }).exec();
    },

    teachingChange(val) {
      this.teaching_type = val;
      this.form.page = 1;
      this.list = [];
      this.getData();
    },

    studyLoginLose() {
      // 处理登录失效逻辑
      console.log('登录失效');
    }
  },

  mounted() {
    this.getData();
    // uni-app中使用页面滚动监听
    setTimeout(() => {
      uni.onPageScroll((e) => {
        this.scrollGet(e);
      });
    }, 500);
  }
}
</script>
<style lang="less" scoped>
.my-course {
  background-color: #f2f5f7;
  min-height: 100vh;
}
</style>
