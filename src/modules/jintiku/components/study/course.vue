<template>
	<view class="study-plan-list">
		<view v-for="(item, index) in list" :key="index" v-if="list && list.length" :class="{
        gaoduan: item.business_type == 2,
        sishu: item.business_type == 3,
      }" class="study-plan-item" @click="goLearnCourseDetails(item)">
			<view v-if="item.teaching_type_name" class="green-left-top">
				{{item.teaching_type_name}}
			</view>
			<view class="study-plan-top">
				<!-- <image v-if="item.business_type == 2" class="business-type-img"
					src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8bf1173295543182774015_%E9%AB%98%E7%AB%AF.png" />
				<image v-if="item.business_type == 3" class="business-type-img"
					src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/632e173295546131194415_%E7%A7%81%E5%A1%BE.png" /> -->
				<text class="study-plan-top-name">{{ item.goods_name }}</text>
			</view>
			<view class="study-plan-time">
				2025年11月5日-2025年11月7日
			</view>
			<!-- <view class="study-plan-time">
				{{ item.class.date }}
			</view> -->
			<view v-if="item.goods_pid && item.goods_pid != '0'" class="study-plan-from">
				<text>套餐：</text>
				<text>{{ item.goods_pid_name }}</text>
			</view>
			<!-- <view v-if="item.teaching_type_name" class="business-type-list">
        <view v-for="(name, nameIndex) in getTeachingTypeNames(item)" :key="nameIndex">
          <view
            :class="{
              'business-type-mianshou': getTeachingTypeByIndex(item, nameIndex) == 2,
              'business-type-lubo': getTeachingTypeByIndex(item, nameIndex) == 3,
              'business-type-zhibo': getTeachingTypeByIndex(item, nameIndex) == 1,
            }"
            class="business-type"
          >
            {{ name }}
          </view>
        </view>
      </view> -->

			<view class="study-plan-teaching-list">
				<view class="study-plan-teaching">
					<image src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixinshare.png" class="teaching-img" />
					<view class="study-plan-teaching-message">
						<view class="teaching-name">金英讲师</view>
						<view class="teaching-title">授课教师</view>
					</view>
				</view>
				<view class="study-plan-teaching">
					<image src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixinshare.png" class="teaching-img" />
					<view class="study-plan-teaching-message">
						<view class="teaching-name">金英讲师</view>
						<view class="teaching-title">授课教师</view>
					</view>
				</view>
				<view class="study-plan-teaching">
					<image src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixinshare.png" class="teaching-img" />
					<view class="study-plan-teaching-message">
						<view class="teaching-name">金英讲师</view>
						<view class="teaching-title">授课教师</view>
					</view>
				</view>
				<!-- <view v-for="(teacher, teacherIndex) in item.class.teacher" :key="teacherIndex"
					class="study-plan-teaching">
					<image v-if="teacher.avatar" :src="completePath(teacher.avatar)" class="teaching-img" />
					<image v-else class="teaching-img"
						src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixindf.png" />
					<view class="study-plan-teaching-message">
						<view class="teaching-name">{{ teacher.name }}</view>
						<view class="teaching-title">{{ teacher.title }}</view>
					</view>
				</view> -->
			</view>
			<view class="today-lesson-operation">
				<view class="today-lesson-operation-box">
					<view v-for="(btn, btnIndex) in item.evaluation_type" :key="btnIndex" class="operation-box-item"
						@click.stop="goAnswer(item, btn)">
						<text class="operation-text">{{ btn.name }}</text>
					</view>
					<!-- <view class="operation-box-item">
						<text class="operation-text">开课测</text>
					</view>
					<view class="operation-box-item">
						<text class="operation-text">评价</text>
					</view> -->
				</view>
			</view>
			
			
			<!-- <view v-if="item.evaluation_type && item.evaluation_type.length" class="today-lesson-operation">	
				<view class="today-lesson-operation-box">
					<view v-for="(btn, btnIndex) in item.evaluation_type" :key="btnIndex" class="operation-box-item"
						@click.stop="goAnswer(item, btn)">
						<image :src="completePath(btn.icon)" class="operation-img" />
						<text class="operation-text">{{ btn.name }}</text>
					</view>
					<view class="operation-box-item">
						<image class="operation-img"
							src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/576c173347914141868015_kaike.png" />
						<text class="operation-text">开课测</text>
					</view>
					<view class="operation-box-item">
						<image class="operation-img"
							src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/9028173310453840513498_pingce.png" />
						<text class="operation-text">评价</text>
					</view>
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	export default {
		name: 'ModuleStudyCourse',
		props: {
			list: {
				type: Array,
				default: () => [],
			},
		},
		methods: {
			// 获取授课形式名称数组
			getTeachingTypeNames(item) {
				if (!item.teaching_type_name) return [];
				return item.teaching_type_name.split(',');
			},
			// 根据索引获取授课形式类型
			getTeachingTypeByIndex(item, index) {
				if (!item.teaching_type) return '';
				const types = item.teaching_type.split(',');
				return types[index] || '';
			},
			goLearnCourseDetails(data) {
				this.$xh.push('jintiku',
					`pages/study/detail/index?goods_id=${data.goods_id}&goods_pid=${data.goods_pid}&order_id=${data.order_id}`
				);
			},
			// 拼接完整路径
			completePath(path) {
				// TODO: 需要配置OSS基础URL
				return 'https://ysys-assets.oss-cn-beijing.aliyuncs.com' + path;
			},
			// 跳转答题
			goAnswer(item, btn) {
				this.$xh.push('jintiku',
					`pages/answertest/answer/index?paper_version_id=${btn.paper_version_id || ""}&evaluation_type_id=${btn.id || ""}&professional_id=${btn.professional_id || ""}&goods_id=${item.paper_goods_id || ""}&order_id=${item.order_id || ""}&system_id=${item.system_id || ""}&order_detail_id=${item.order_goods_detail_id || ""}`
				);
			},
		},
	};
</script>

<style lang="less" scoped>
	.study-plan-list {
		padding: 16px 17px 13px 16px;



		.study-plan-item {
			background: #fff;
			margin-bottom: 10px;
			border-radius: 32rpx;
			padding: 17px 16px 14px 16px;
			position: relative; // 为子元素的绝对定位提供参考

			.green-left-top {
				background-color: #04C140;
				position: absolute;
				left: 0;
				top: 0;
				width: 160rpx;
				height: 50rpx;
				z-index: 1;
				border-radius: 32rpx 0 32rpx 0; // 可选：让标签的圆角与父容器匹配
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 12px;
				font-weight: 500;
			}

			.study-plan-top {
				margin-top:50rpx;

				.business-type-img {
					transform: translateY(-1px);
					width: 63rpx;
					height: 21px;
					vertical-align: middle;
					margin-right: 6px;
				}

				.study-plan-top-name {
					vertical-align: middle;
					font-weight: 600;
					font-size: 17px;
					line-height: 20px;
					color: #262629;

					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					word-break: break-all;
					line-height: 1.4;
					max-height: calc(1.4em * 2);
				}
			}

			.study-plan-time {
				font-weight: 400;
				font-size: 12px;
				color: #4783DC;
				margin-top: 15rpx;
			}

			.business-type-list {
				display: flex;
				justify-content: flex-start;
				margin-top: 10px;
				margin-bottom: 20px;

				.business-type {
					display: inline-block;
					padding-left: 4px;
					padding-right: 4px;
					height: 17px;
					border-radius: 2px;
					text-align: center;
					font-weight: 400;
					font-size: 11px;
					background: #ffefdc;
					color: #ff6304;
					margin-right: 8px;
					width: 31px;
					line-height: 17.8px;
				}

				.business-type-mianshou {
					background: #ffefdc;
					color: #ff6304;
				}

				.business-type-lubo {
					background: #ececf4;
					color: #26314b;
				}

				.business-type-zhibo {
					background: #dcf5ec;
					color: #018CFF;
				}
			}

			.study-plan-from {
				font-weight: 400;
				font-size: 11px;
				color: #4783DC;
				margin-bottom: 10px;


			}

			.study-plan-teaching-list {
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
				font-weight: 500;
				font-size: 11px;
				color: #262629;
				overflow: hidden;
				padding: 32rpx 0rpx;

				.study-plan-teaching {
					display: flex;
					justify-content: flex-start;
					align-items: center;
					
					.teaching-img {
						width: 80rpx;
						height: 80rpx;
						margin-right: 5px;
						border-radius: 50%;
						border: 1px solid green;
					}

					.study-plan-teaching-message {
						height: 35rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: flex-start;
						font-size: 24rpx;
						transform: translateY(1px);

						.teaching-name {
							margin-bottom: 3px;
						}

						.teaching-title {
							color: rgba(38, 38, 41, 0.6);
						}
					}
				}
			}

			.today-lesson-operation {
				display: flex;
				justify-content: flex-end;
				align-items: center;
				padding-top: 14rpx;

				.go-learn {
					font-weight: 400;
					font-size: 11px;
					color: #93969f;
				}

				.today-lesson-operation-box {
					display: flex;
					justify-content: flex-start;
					font-weight: 400;
					font-size: 20rpx;
					color: #000000;

					.operation-box-item {
						display: flex;
						justify-content: flex-start;
						align-items: center;
						border: 3rpx solid #000000;
						border-radius: 44rpx;
						padding: 16rpx 30rpx;
						margin-left: 24rpx;
					}
				}
			}
		}

		.gaoduan {
			background: transparent;
			background-image: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/61c7173510867228878785_gaoduan.png");
			background-size: 100% 100%;
			background-repeat: no-repeat;
		}

		.sishu {
			background: transparent;
			background-image: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6c3f173510870217835730_sishu.png ");
			background-size: 100% 100%;
			background-repeat: no-repeat;
		}

		.study-plan-item:last-child {
			margin-bottom: 0;
		}
	}
</style>