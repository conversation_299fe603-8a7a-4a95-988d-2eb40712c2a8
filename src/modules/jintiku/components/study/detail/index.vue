<template>
  <div class="learn-course-details">
    <div v-if="courseInfo.goods_id" class="learn-course-details-title-box">
      <div class="learn-course-details-title">
        <img
          v-if="courseInfo.business_type == 2"
          class="learn-course-details-business-type"
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/8bf1173295543182774015_%E9%AB%98%E7%AB%AF.png"
        />
        <img
          v-if="courseInfo.business_type == 3"
          class="learn-course-details-business-type"
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/632e173295546131194415_%E7%A7%81%E5%A1%BE.png"
        />
        <text class="learn-course-details-name">
          {{ courseInfo.goods_name }}
        </text>
      </div>
      <div class="learn-course-details-time">
        <text class="learn-course-details-time-text"
          >{{ courseInfo.class.date }}
        </text>
      </div>
      <div class="learn-course-details-progress">
        <div class="learn-course-details-progress-text">学习进度</div>
        <div class="learn-course-details-progress-progress">
          <u-line-progress
            v-if="
              courseInfo.class&&courseInfo.class.lesson_attendance_num &&
              courseInfo.class.lesson_attendance_num != '0' &&
              courseInfo.class.lesson_num &&
              courseInfo.class.lesson_num != '0'
            "
            :percent="
              getProgress(
                courseInfo.class.lesson_attendance_num,
                courseInfo.class.lesson_num
              )
            "
            :show-percent="false"
            active-color="#018CFF"
          ></u-line-progress>
          <u-line-progress
            v-else
            :show-percent="false"
            :percent="0"
            active-color="#018CFF"
          ></u-line-progress>
        </div>
        <div class="learn-course-details-progress-text-percent">
          {{
            getProgress(
              courseInfo.class.lesson_attendance_num,
              courseInfo.class.lesson_num
            ) + "%"
          }}
        </div>
      </div>
      <div class="learn-course-details-teacher">
        <div
          v-for="item in courseInfo.class.teacher"
          class="learn-course-details-teacher-item"
        >
          <div class="learn-course-details-teacher-item-img">
            <img
              v-if="item.avatar"
              :src="completePath(item.avatar)"
              class="teacher-img"
            />
            <img
              v-else
              class="teacher-img"
              src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/yakaixindf.png"
            />
          </div>
          <div class="learn-course-details-teacher-item-text">
            <div class="learn-course-details-teacher-item-text-rank">
              {{ item.name }}
            </div>
            <div class="learn-course-details-teacher-item-text-name">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
      <!--      <div class="learn-course-details-people">-->
      <!--        &lt;!&ndash;        <div class="learn-course-details-people-box">&ndash;&gt;-->
      <!--        &lt;!&ndash;          <div class="dot"></div>&ndash;&gt;-->
      <!--        &lt;!&ndash;          <div class="num"> {{ courseInfo.people }}人观看</div>&ndash;&gt;-->
      <!--        &lt;!&ndash;        </div>&ndash;&gt;-->
      <!--        <div class="transmit">-->
      <!--          <img-->
      <!--              class="transmit-img"-->
      <!--              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1898173314373968215101_zhuanfa.png"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </div>-->
      <div class="learn-course-details-operation">
        <div class="learn-course-details-add-wechat" @click="addStudy">
          <img
            class="learn-course-details-add-wechat-img"
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/47a7173314345401542129_weixin.png"
          />
          <span>添加学管</span>
        </div>
        <div class="learn-course-details-add-wechat">
          <img
            class="learn-course-details-add-wechat-img"
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/5fdc173614999687080004_%E7%BC%96%E7%BB%84%2016%E5%A4%87%E4%BB%BD%204%402x.png"
          />
          <span>分享课程</span>
        </div>
        <!--        <div class="learn-course-details-learning-log">-->
        <!--          <img-->
        <!--              class="learn-course-details-learning-log-img"-->
        <!--              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/4fea173314349647477492_xueqing.png"-->
        <!--          />-->
        <!--          <text>学情记录</text>-->
        <!--        </div>-->
        <!--        <div class="learn-course-details-learning-remind">-->
        <!--          <text>上课提醒</text>-->
        <!--          <u-switch v-model="courseInfo.isRemind"></u-switch>-->
        <!--        </div>-->
      </div>
    </div>
    <div v-if="recentlyData.lesson_id" class="go-on-learn">
      <div class="go-on-learn-describe">
        <img
          class="go-on-learn-img"
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/5022173314276286241077_%E6%92%AD%E6%94%BE.png"
        />
        <div class="go-on-learn-name">
          {{
            recentlyData.lesson_name.length <= 12
              ? recentlyData.lesson_name
              : recentlyData.lesson_name.slice(0, 12) + "..."
          }}
        </div>
      </div>
      <div
        class="go-on-learn-btn"
        @click="goLookCourse(recentlyData.lesson_id)"
      >
        <span>继续学习</span>
      </div>
    </div>
    <div v-if="learnCourseList.length" class="learn-course-list">
      <div v-for="item in learnCourseList" class="learn-course-list-item">
        <div class="learn-course-list-title">
          <div class="title">
            <div class="title-name">
              <span class="teaching-type">{{ item.teaching_type_name }}</span>
              <span style="color: #262629; font-weight: 500">{{
                item.name
              }}</span>
              <span class="label"
                >（{{ item.lesson_attendance_num?item.lesson_attendance_num:'0' }}/{{
                  item.lesson_num?item.lesson_num:'0'
                }}）</span
              >
            </div>
            <div class="close-open">
              <img
                v-if="!item.isClose"
                class="close"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/31c3173314287462623784_%E4%B8%8A.png"
                @click="item.isClose = true"
              />
              <img
                v-else
                class="open"
                src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/57dd17331428838889503_%E4%B8%8B.png"
                @click="item.isClose = false"
              />
            </div>
          </div>
          <div v-if="item.address" class="address">
            <img
              class="address-img"
              src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/53a1173442499072733755_dizhi.png"
            />
            {{ item.address }}
          </div>
          <div class="class-progress">
            <span class="label">班级进度</span>
            <div class="progress">
              <u-line-progress
                v-if="
                  courseInfo&&courseInfo.class&&courseInfo.class.lesson_attendance_num &&
                  courseInfo.class.lesson_attendance_num != '0' &&
                  courseInfo.class.lesson_num &&
                  courseInfo.class.lesson_num != '0'
                "
                :percentage="
                  getProgress(
                    courseInfo.class.lesson_attendance_num,
                    courseInfo.class.lesson_num
                  )
                "
                :show-pivot="false"
                track-color="#D3F4E2"
                color="#018CFF"
              />
              <u-line-progress
                v-else
                :percentage="0"
                track-color="#D3F4E2"
                :show-pivot="false"
                color="#018CFF"
              />
            </div>
            <span class="label"
              >{{ item.lesson_attendance_num?item.lesson_attendance_num:'0' }}/{{ item.lesson_num?item.lesson_num:'0' }}</span
            >
          </div>
        </div>
        <div v-if="!item.isClose" class="learn-course-list-content">
          <div
            v-for="(lessons, index) in item.lesson"
            class="learn-course-lessons"
          >
            <div
              class="learn-course-lessons-name"
              @click="goLookCourse(lessons.lesson_id, item.teaching_type)"
            >
              <span
                style="
                  color: #ff900d;
                  font-weight: 600;
                  font-size: 16px;
                  margin-right: 8px;
                  font-family: PingFangSC, PingFang SC;
                "
                >{{ index >= 10 ? "" : 0 }}{{ index + 1 }}</span
              >
              {{ lessons.lesson_name }}
            </div>
            <div
              class="learn-course-lessons-assistant-name"
              @click="lessons.isOpen = !lessons.isOpen"
              v-if="lessons.lesson_name_other"
            >
              <div
                class="learn-course-lessons-assistant-name-name"
                :class="{
                  'learn-course-lessons-assistant-name-show': lessons.isOpen,
                }"
              >
                {{ lessons.lesson_name_other }}
              </div>
              <div
                v-if="lessons.lesson_name_other.length > 23"
                class="learn-course-lessons-assistant-name-operate"
              >
                <span class="open" v-if="!lessons.isOpen">展开</span>
                <span class="close" v-else>收起</span>
              </div>
            </div>
            <div
              class="learn-course-lessons-message"
              @click="goLookCourse(lessons.lesson_id, item.teaching_type)"
            >
              <div class="learn-course-lessons-message-left">
                <div
                  v-if="item.teaching_type == '1'"
                  class="learn-course-lessons-type"
                >
                  <img
                    v-if="lessons.lesson_status == '3'"
                    class="learn-course-lessons-type-img"
                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/2759173314312481144525_huifang.png"
                  />
                  <!--                  <img-->
                  <!--                      v-if="lessons.status == '3'"-->
                  <!--                      class="learn-course-lessons-type-img"-->
                  <!--                      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/531817331432239781486_shengchenghuifang.png"-->
                  <!--                  />-->
                  <img
                    v-if="lessons.lesson_status != '3'"
                    class="learn-course-lessons-type-img"
                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/896f173314326200816744_zhibo.png"
                  />
                  <!--                  <img-->
                  <!--                    class="learn-course-lessons-type-img"-->
                  <!--                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/52fd173314329947918956_shipin.png-->
                  <!--"-->
                  <!--                  />-->
                  <text>{{
                    lessons.lesson_status == "3" ? "回放" : "直播"
                  }}</text>
                </div>
                <div v-if="item.teaching_type == '1'" class="line"></div>
                <div class="learn-course-lessons-time">{{ lessons.date }}</div>
                <!--                <div class="line"></div>-->
                <!--                <div class="learn-course-lessons-teacher">-->
                <!--                  {{ lessons.teacher }}-->
                <!--                </div>-->
              </div>
              <div class="learn-course-lessons-message-right">
                <span v-if="lessons.status == '2'">已学完</span>
                <!--                <span v-if="lessons.status=='2'" style="color: #018CFF">正在学</span>-->
                <span v-if="lessons.status != '2'" style="color: #018CFF"
                  >未学习</span
                >
              </div>
            </div>

            <div
              :style="{
                paddingBottom:
                  lessons.evaluation_type_top.length ||
                  lessons.resource_document.length
                    ? '20px'
                    : '0px',
                borderBottom:
                  lessons.evaluation_type_bottom &&
                  lessons.evaluation_type_bottom.length
                    ? '1px solid rgba(232,233,234,.6)'
                    : 'none',
              }"
              class="operation-box"
            >
              <div
                v-for="btn in lessons.evaluation_type_top"
                class="operation-box-item preview"
                @click="goAnswer(lessons, btn)"
              >
                <img :src="completePath(btn.icon)" class="operation-img" />
                <text class="operation-text">{{ btn.name }}</text>
              </div>
              <div
                v-if="lessons.resource_document.length"
                class="operation-box-item preview"
                @click="goDataDownload(lessons.resource_document[0].path)"
              >
                <img
                  class="operation-img"
                  src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/6473173622834392761808_ziliao.png"
                />
                <text class="operation-text">资料</text>
              </div>
              <!--              <div v-if="lessons.pingjia == '1'" class="operation-box-item preview">-->
              <!--                <img-->
              <!--                    class="operation-img"-->
              <!--                    src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/9028173310453840513498_pingce.png"-->
              <!--                ></img>-->
              <!--                <text class="operation-text">评价</text>-->
              <!--              </div>-->
            </div>
            <div
              v-if="
                lessons.evaluation_type_bottom &&
                lessons.evaluation_type_bottom.length
              "
              class="independent-list"
            >
              <div
                v-for="independent in lessons.evaluation_type_bottom"
                class="independent-item"
              >
                <div class="independent-list-item-title">
                  <img
                    :src="completePath(independent.icon)"
                    alt=""
                    class="independent-list-item-title-img"
                  />
                  <span class="independent-list-item-title-name">{{
                    independent.name
                  }}</span>
                </div>
                <div
                  v-if="independent.is_evaluation == '1'"
                  class="independent-list-item-operate over"
                  @click="goAnswer(lessons, independent)"
                >
                  <span>已完成</span>
                </div>
                <div
                  v-if="independent.is_evaluation == '2'"
                  class="independent-list-item-operate go-exam"
                  @click="goAnswer(lessons, independent)"
                >
                  <span>去考试</span>
                </div>
                <!--                <div v-if="independent.status== '3'" class="independent-list-item-operate unlock">-->
                <!--                  <img alt=""-->
                <!--                       src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/795d173431941362452725_suo2.png">-->
                <!--                  <span>解锁</span>-->
                <!--                </div>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="loading" style="text-align: center; margin-top: 50px">
      <u-loading-icon />
    </div>
    <u-popup
      v-model:show="showStudyDialog"
      :show-cancel-button="false"
      :show-confirm-button="false"
      close-on-click-overlay
    >
      <div class="qr-code">
        <div class="qr-code-title">添加学管老师</div>
        <div class="qr-code-title-describe">协助激活课程，提供售后答疑</div>
        <div class="qr-code-img">
          <u-image
            :src="completePath(courseInfo.manager_exwechat)"
            width="100%"
          />
        </div>
        <div class="qr-code-bottom-describe">长按扫一扫，添加企业微信</div>
      </div>
    </u-popup>
    <img
      v-if="showStudyDialog"
      class="qr-code-close"
      src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1f89173458174239532966_guanbi.png"
      @click="qrCodeClose"
    />
  </div>
</template>
<script>
import { study } from '../../../api/index.js'

export default {
  data() {
    return {
      courseInfo: {},
      recentlyData: {
        lesson_id: "",
        lesson_name: "",
      },
      learnCourseList: [],
      loading: true,
      showStudyDialog: false,
    };
  },
  async mounted() {
    await this.getCourseDetail();
    await this.getCourseDetailLessons();
    await this.getCourseDetailRecently();
  },
  methods: {
    async getCourseDetail() {
      const query = this.$mp && this.$mp.query ? this.$mp.query : {};

      study
        .courseDetail({
          goods_id: query.goods_id || "",
          goods_pid: query.goods_pid || "",
          order_id: query.order_id || "",
          page: 1,
          size: 10,
        })
        .then(({ data }) => {
          if (data && data.list && data.list.length) {
            this.courseInfo = data.list[0];
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err.msg && err.msg[0] ? err.msg[0] : '获取课程详情失败',
            icon: 'none'
          });
          if (err.code == 100002) {
            // Handle login expiry
            this.$store.commit('jintiku/setToken', '');
            this.$xh.push('jintiku', 'pages/login/index');
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async getCourseDetailLessons() {
      const query = this.$mp && this.$mp.query ? this.$mp.query : {};

      study
        .courseDetailLessons({
          goods_id: query.goods_id || "",
          goods_pid: query.goods_pid || "",
          order_id: query.order_id || "",
        })
        .then(({ data }) => {
          if (data && data.length) {
            this.learnCourseList = data;
            this.learnCourseList = this.learnCourseList.map((item) => {
              return {
                ...item,
                lesson: item.lesson.map((lesson) => {
                  return {
                    ...lesson,
                    evaluation_type_top: lesson.evaluation_type.filter(
                      (btn) =>
                        btn.is_separate == "2" &&
                        btn.paper_version_id &&
                        btn.paper_version_id != "0"
                    ),
                    evaluation_type_bottom: lesson.evaluation_type.filter(
                      (btn) =>
                        btn.is_separate == "1" &&
                        btn.paper_version_id &&
                        btn.paper_version_id != "0"
                    ),
                  };
                }),
              };
            });
          } else {
            this.learnCourseList = [];
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err.msg && err.msg[0] ? err.msg[0] : '获取课程列表失败',
            icon: 'none'
          });
          if (err.code == 100002) {
            this.$store.commit('jintiku/setToken', '');
            this.$xh.push('jintiku', 'pages/login/index');
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async getCourseDetailRecently() {
      const query = this.$mp && this.$mp.query ? this.$mp.query : {};

      study
        .courseDetailRecently({
          goods_id: query.goods_id || "",
          goods_pid: query.goods_pid || "",
          order_id: query.order_id || "",
        })
        .then(({ data }) => {
          if (data && data.lesson_id) {
            this.recentlyData = data;
          } else {
            this.recentlyData = {
              lesson_id: "",
              lesson_name: "",
            };
          }
        })
        .catch((err) => {
          console.log(err);
          uni.showToast({
            title: err.msg && err.msg[0] ? err.msg[0] : '获取最近学习失败',
            icon: 'none'
          });
          if (err.code == 100002) {
            this.$store.commit('jintiku/setToken', '');
            this.$xh.push('jintiku', 'pages/login/index');
          }
        });
    },
    async goLookCourse(lesson_id, teaching_type) {
      if (teaching_type && teaching_type != "1") {
        return;
      }
      const query = this.$mp && this.$mp.query ? this.$mp.query : {};

      study
        .liveUrl({
          lesson_id: lesson_id,
        })
        .then(({ data }) => {
          if (data.playback_url) {
            this.$xh.push('jintiku', `pages/study/video/index?url=${encodeURIComponent(data.playback_url)}&filter_goods_id=${data.goods_id || ""}&goods_id=${query.goods_id || ""}&goods_pid=${query.goods_pid || ""}&order_id=${query.order_id || ""}&lesson_id=${lesson_id}`);
          } else {
            this.$xh.push('jintiku', `pages/study/live/index?lesson_id=${lesson_id}&url=${encodeURIComponent(data.live_url)}`);
          }
        })
        .catch(() => {
          uni.showToast({
            title: "获取直播地址失败！",
            icon: 'none'
          });
        });
    },
    goDataDownload(url) {
      if (!url) {
        uni.showToast({
          title: "暂无资料",
          icon: 'none'
        });
        return;
      }
      // Use uni-app's preview functionality
      uni.downloadFile({
        url: this.completePath(url),
        success: (res) => {
          uni.openDocument({
            filePath: res.tempFilePath,
            success: () => {
              console.log('打开文档成功');
            }
          });
        }
      });
    },
    addStudy() {
      if (!this.courseInfo.manager_exwechat) {
        uni.showToast({
          title: "暂未获取到学管信息！",
          icon: 'none'
        });
        return;
      }
      this.showStudyDialog = true;
    },
    qrCodeClose() {
      this.showStudyDialog = false;
    },
    goAnswer(lessons, btn) {
      this.$xh.push('jintiku', `pages/answertest/answer/index?paper_version_id=${btn.paper_version_id || ""}&evaluation_type_id=${btn.id || ""}&professional_id=${btn.professional_id || ""}&goods_id=${lessons.paper_goods_id || ""}&order_id=${lessons.order_id || ""}&system_id=${lessons.system_id || ""}&order_detail_id=${lessons.order_goods_detail_id || ""}&lesson_id=${lessons.lesson_id || ""}`);
    },
    // 拼接完整路径
    completePath(path) {
      return process.env.VUE_APP_BASEOSSURL + path;
    },
    getProgress(overNum, totalNum) {
      if (!totalNum || !overNum || totalNum == "0" || overNum == "0") {
        return 0;
      }
      return Math.round(Number((overNum / totalNum) * 100));
    },
  },
};
</script>

<style lang="scss" scoped>
.learn-course-details {
  background-color: #f2f5f7;
  font-size: 12px;
  padding: 12px;
  min-height: 100vh;
  border-radius: 6px;
  .learn-course-details-title-box {
    box-sizing: border-box;
    background: #fff;
    border-radius: 12rpx;
    padding: 17px 16px 16px 16px;
    border-radius: 6px;

    .learn-course-details-title {
      font-weight: 600;
      font-size: 16px;
      color: #262629;
      margin-bottom: 6px;

      .learn-course-details-business-type {
        transform: translateY(-1px);
        height: 21px;
        width: 63px;
        vertical-align: middle;
        margin-right: 4px;
      }

      .learn-course-details-name {
        line-height: 22px;
        vertical-align: middle;
      }
    }

    .learn-course-details-time {
      margin-bottom: 20px;

      .learn-course-details-time-text {
        font-weight: 400;
        font-size: 12px;
        color: rgba(66, 75, 87, 0.85);
      }
    }

    .learn-course-details-progress {
      margin-bottom: 20px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .learn-course-details-progress-text {
        font-weight: 400;
        font-size: 12px;
        color: #424b57;
        margin-right: 9px;
      }
      .learn-course-details-progress-progress {
        flex: 1;
      }
      .learn-course-details-progress-text-percent {
        font-size: 12px;
        color: #018CFF;
        text-align: right;
        margin-left: 14px;
      }
    }

    .learn-course-details-teacher {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      margin-bottom: 18px;
      font-size: 10px;
      max-height: 31.5px;
      overflow: hidden;

      .learn-course-details-teacher-item {
        width: 33.3%;
        display: flex;
        justify-content: flex-start;

        .learn-course-details-teacher-item-img {
          margin-right: 10px;

          .teacher-img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            overflow: hidden;
          }
        }
        .learn-course-details-teacher-item-text {
          height: 30px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-content: center;
          transform: translateY(1px);

          .learn-course-details-teacher-item-text-rank {
            font-weight: 400;
            font-size: 10px;
            color: rgba(38, 38, 41, 1);
            margin-bottom: 5px;
          }

          .learn-course-details-teacher-item-text-name {
            font-weight: 400;
            font-size: 10px;
            color: rgba(38, 38, 41, 0.6);
          }
        }
      }
    }

    .learn-course-details-people {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      border-bottom: 1px solid rgba(232, 233, 234, 0.6);
      padding-bottom: 13px;
      margin-bottom: 12px;

      .learn-course-details-people-box {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .dot {
          width: 4px;
          height: 4px;
          background: #018CFF;
          border-radius: 2px;
          margin-right: 6px;
        }
      }

      .transmit {
        .transmit-img {
          width: 16px;
          height: 16px;
        }
      }
    }

    .learn-course-details-operation {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-top: 16px;
      border-top: 1px solid rgba(232, 233, 234, 0.6);

      .learn-course-details-add-wechat {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        //margin-right: 38px;
        color: #424b57;

        .learn-course-details-add-wechat-img {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }
      .learn-course-details-add-wechat:not(:first-child) {
        margin-left: 38px;
      }

      .learn-course-details-learning-log {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .learn-course-details-learning-log-img {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }

      //.learn-course-details-learning-remind {
      //  display: flex;
      //  justify-content: flex-end;
      //  align-items: center;
      //  text {
      //    margin-right: 12rpx;
      //  }
      //}
    }
  }

  .go-on-learn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    margin-top: 11px;
    padding: 12px 16px;
    border-radius: 6px;

    .go-on-learn-describe {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .go-on-learn-img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
      .go-on-learn-name {
        font-weight: 500;
        font-size: 14px;
        color: #262629;
        line-height: 20px;
      }
    }

    .go-on-learn-btn {
      box-sizing: border-box;
      font-weight: 400;
      font-size: 12px;
      color: #018CFF;
      text-align: center;
      height: 28px;
      width: 72px;
      min-width: 72px;
      background: rgba(1, 163, 99, 0.07);
      border-radius: 14px;
      border: 1px solid rgba(1, 163, 99, 0.47);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .learn-course-list {
    margin-top: 11px;

    .learn-course-list-item {
      background-color: #fff;
      border-radius: 6px;
      overflow: hidden;
      background-image: url("https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/44fe173596384986478984_bga.png");
      //background-size: cover;
      background-size: 100% 254px;
      background-repeat: no-repeat;

      .learn-course-list-title {
        //background: linear-gradient(180deg, #E8FFF2 0%, #F1FFF8 50%, #F4FFFB 100%);
        padding: 18px;

        .title {
          font-weight: 500;
          font-size: 15px;
          color: #262629;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 14px;

          .title-name {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .teaching-type {
              width: 31px;
              height: 17px;
              background: #018CFF;
              border-radius: 2px;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              font-weight: 400;
              font-size: 11px;
              color: #ffffff;
              margin-right: 6px;
            }
          }

          .close-open {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .close {
              width: 17px;
              height: 17px;
            }

            .open {
              width: 17px;
              height: 17px;
            }
          }
        }

        .address {
          .address-img {
            width: 14px;
            height: 14px;
            margin-right: 5px;
          }

          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          line-height: 16px;
          font-weight: 400;
          font-size: 12px;
          color: #424b57;
          margin-bottom: 16px;
        }

        .class-progress {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .label {
            font-weight: 400;
            font-size: 12px;
            color: #424b57;
          }

          .progress {
            margin: 0px 17px 0px 9px;

            flex: 1;
          }
        }
      }

      .learn-course-list-content {
        box-sizing: border-box;
        padding: 18px 16px 0px;
        background: #ffffff;
        margin-left: 2px;
        margin-right: 2px;

        .learn-course-lessons {
          margin-bottom: 19px;
          border-bottom: 1px solid rgba(232, 233, 234, 0.6);
          //padding-bottom:11px;

          .learn-course-lessons-name {
            margin-bottom: 11px;
            font-weight: 600;
            font-size: 15px;
            color: #262629;
            line-height: 22px;
          }
          .learn-course-lessons-assistant-name {
            margin-bottom: 10px;
            line-height: 16px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            font-size: 13px;
            .learn-course-lessons-assistant-name-name {
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #424b57;
            }
            .learn-course-lessons-assistant-name-operate {
              width: 30px;
              text-align: right;
              color: #018CFF;
            }
            .learn-course-lessons-assistant-name-show {
              overflow: auto;
              text-overflow: clip;
              white-space: normal;
            }
          }
          .learn-course-lessons-message {
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-weight: 400;
            font-size: 12px;
            color: #93969f;
            background: #fff6e3;
            height: 32px;
            padding-left: 10px;
            padding-right: 10px;
            border-radius: 4px;

            .learn-course-lessons-message-left {
              display: flex;
              justify-content: flex-start;
              align-items: center;

              .learn-course-lessons-type {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-weight: 400;
                font-size: 11px;
                color: #424b57;

                .learn-course-lessons-type-img {
                  width: 12px;
                  height: 12px;
                  margin-right: 7px;
                }
              }

              .line {
                width: 1px;
                height: 10px;
                background: #e1e5e8;
                margin: 0 8px;
              }

              .learn-course-lessons-time {
                color: #424b57;
                font-size: 11px;
              }


            }

            .learn-course-lessons-message-right {
              font-size: 11px;
            }
          }

          .operation-box {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #424b57;
            padding-bottom: 22px;

            .operation-box-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
            }

            .operation-box-item:not(:last-child) {
              position: relative;
              padding-right: 24px;
              margin-right: 24px;
            }

            .operation-box-item:not(:last-child)::after {
              content: "";
              width: 1px;
              height: 10px;
              background: #eceff1;
              position: absolute;
              right: 0;
              top: 2px;
            }

            .operation-img {
              width: 14px;
              height: 14px;
              margin-right: 3px;
            }

            .operation-text {
              color: #424b57;
            }
          }

          .no-border {
            border-bottom: none;
          }

          .independent-list {
            padding-top: 10px;
            padding-bottom: 10px;
            .independent-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px 0;
              //border-bottom: 1px solid #E8E9EA;

              .independent-list-item-title {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .independent-list-item-title-img {
                  width: 24px;
                  height: 24px;
                  margin-right: 8px;
                }

                .independent-list-item-title-name {
                  font-weight: 600;
                  font-size: 14px;
                  color: #262629;
                }
              }

              .independent-list-item-operate {
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: 400;
                font-size: 12px;
                color: #93969f;
                margin-right: 18px;
              }



              .go-exam {
                margin-right: 0px;
                font-weight: 400;
                font-size: 12px;
                color: #018CFF;
                width: 72px;
                height: 28px;
                border-radius: 14px;
                border: 1px solid rgba(1, 163, 99, 0.56);
                text-align: center;
                line-height: 26px;
              }

              .over {
                margin-right: 0px;
                font-weight: 400;
                font-size: 12px;
                width: 72px;
                height: 28px;
                border-radius: 14px;
                border: 1px solid #e2e2e2;
                text-align: center;
                line-height: 26px;
              }

              .unlock {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 0px;
                font-weight: 400;
                font-size: 12px;
                color: #969696;
                width: 72px;
                height: 28px;
                border-radius: 14px;
                border: 1px solid transparent;
                background: #efefef;

                img {
                  width: 14px;
                  height: 14px;
                  margin-right: 2px;
                }
              }
            }
          }
        }
      }

      .learn-course-lessons:last-child {
        margin-bottom: 0;
        border-bottom: 0px;

        .independent-list {
          .independent-item:last-child {
            border-bottom: 0px;
          }
        }
      }
    }

    .learn-course-list-item:not(:first-child) {
      margin-top: 12px;
    }
  }

  .qr-code {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 25px;
    padding-bottom: 23px;

    .qr-code-title {
      font-weight: 500;
      font-size: 20px;
      color: #03203d;
      margin-bottom: 12px;
    }

    .qr-code-title-describe {
      font-weight: 400;
      font-size: 15px;
      color: #03203d;
      margin-bottom: 12px;
    }

    .qr-code-img {
      box-sizing: border-box;
      padding: 13px;
      background-color: #f2f6f9;
      width: 190px;
    }

    .qr-code-bottom-describe {
      margin-top: 12px;
      font-weight: 400;
      font-size: 15px;
      color: rgba(3, 32, 61, 0.75);
    }
  }

  .qr-code-close {
    width: 30px;
    height: 30px;
    z-index: 3000;
    position: fixed;
    left: calc(50% - 15px);
    top: calc(50% + 171px);
  }
}
</style>
