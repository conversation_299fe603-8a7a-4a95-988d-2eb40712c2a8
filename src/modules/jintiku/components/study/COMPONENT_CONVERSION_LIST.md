# Study组件转换清单

## 组件列表及转换状态

### ✅ 已转换完成
1. **lessonsList.vue** - 课节列表组件
   - 转换状态：✅ 完成
   - 主要修改：Vue 3 → Vue 2，div → view，img → image，span → text
   - API调用：需要添加 liveUrl API

2. **selectDate.vue** - 日期选择组件
   - 转换状态：✅ 完成
   - 主要修改：div → view，span → text，img → image
   - 依赖：uni-calendar组件

3. **courseList.vue** - 课程列表组件
   - 转换状态：✅ 完成
   - 主要修改：Vue 3 → Vue 2，Composition API → Options API

4. **notLearn.vue** - 无学习内容组件
   - 转换状态：✅ 完成
   - 主要修改：div → view，img → image，路由调用改为uni-app方式

### ✅ 已转换完成（续）

5. **course.vue** - 课程组件
   - 转换状态：✅ 完成
   - 主要修改：已经是Vue 2语法，无需转换

6. **detail/index.vue** - 课程详情组件
   - 转换状态：✅ 完成
   - 主要修改：div → view，img → image，span → text，移除可选链操作符，v-model:show → :show + @close，scss → less

7. **myCourse/index.vue** - 我的课程组件
   - 转换状态：✅ 完成
   - 主要修改：Vue 3 → Vue 2，div → view，Composition API → Options API，DOM滚动监听 → uni-app页面滚动监听

8. **myCourse/teachingTypeTab.vue** - 授课类型标签组件
   - 转换状态：✅ 完成
   - 主要修改：Vue 3 → Vue 2，div → view，modelValue → value，scss → less

9. **dataDownload/index.vue** - 资料下载组件
   - 转换状态：✅ 完成
   - 主要修改：Vue 3 → Vue 2，div → view，img → image，van-search → u-search，Composition API → Options API，window.open → uni.downloadFile

10. **live/baijiayunLive.vue** - 百家云直播组件
    - 转换状态：✅ 完成
    - 主要修改：Vue 3 → Vue 2，div → view，iframe → web-view，useRoute → $mp.query

11. **video/baijiayunPlayback.vue** - 百家云回放组件
    - 转换状态：✅ 完成
    - 主要修改：Vue 3 → Vue 2，div → view，iframe → web-view，Composition API → Options API，showFailToast → uni.showToast

12. **video/courseList.vue** - 视频课程列表组件
    - 转换状态：✅ 完成
    - 主要修改：Vue 3 → Vue 2，div → view，img → image，span → text，移除可选链操作符，Composition API → Options API，van-progress → u-line-progress

### 📦 uni-calendar组件
13. **uni-calendar/** - 日历组件目录
    - 转换状态：❌ 待检查
    - 说明：这是一个完整的日历组件，需要检查是否兼容uni-app

## 转换规则总结

### 模板转换
- `<div>` → `<view>`
- `<span>` → `<text>`
- `<img>` → `<image>`
- 添加 `:key` 属性到 `v-for` 循环
- 移除可选链操作符 `?.` 改为显式判断

### 脚本转换
- Vue 3 Composition API → Vue 2 Options API
- `<script setup lang="ts">` → `<script>`
- 移除 TypeScript 类型注解
- `useRouter()` → `this.$xh.push()`
- `useApi()` → 直接导入API函数
- `props` 定义方式改变
- `emit` 使用方式改变

### 样式转换
- `lang="scss"` → `lang="less"`

### API调用转换
- 需要在 `src/modules/jintiku/api/index.js` 中添加对应的API函数
- 错误处理改为使用 `uni.showToast`
- 路由跳转改为使用 `this.$xh.push`

## 转换完成总结

✅ **所有组件转换完成！** 共转换了12个组件：

### 下一步建议

1. ✅ 检查uni-calendar组件的兼容性
2. ✅ 添加缺失的API函数到 `src/modules/jintiku/api/index.js`
3. 🔄 测试所有组件的功能
4. 🔄 修复可能出现的兼容性问题
5. 🔄 在实际页面中集成和测试这些组件

## 注意事项

1. 所有组件名称需要以 `ModuleStudy` 开头
2. 确保所有API调用都有对应的实现
3. 注意uni-app的组件使用规范
4. 保持原有的功能逻辑不变
